<div>
    <div class="grid grid-cols-1 gap-0 md:grid-cols-12">
        <div
            class="bg-whitedark:bg-gray-800 flex items-center justify-center text-center"
            wire:ignore
        >
            {{-- <input type="text" id="title" class="bg-white outline-none border-gray-100 text-center  text-gray-700 text-sm  focus:ring-red-600 focus:border-red-600 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-red-600 dark:focus:border-red-600"> --}}
        </div>
        <div
            class="bg-whitedark:bg-gray-800 flex items-center justify-center text-center"
            wire:ignore
        >
            {{-- <input type="text" id="title" class="bg-white outline-none border-gray-100 text-center  text-gray-700 text-sm  focus:ring-red-600 focus:border-red-600 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-red-600 dark:focus:border-red-600"> --}}
        </div>
        <div
            class="bg-whitedark:bg-gray-800 flex items-center justify-center text-center md:col-span-4"
            wire:ignore
        >
            {{-- <input type="text" id="title" class="bg-white outline-none border-gray-100  text-gray-700 text-sm  focus:ring-red-600 focus:border-red-600 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-red-600 dark:focus:border-red-600"> --}}
        </div>
        <div
            class="flex items-center justify-center border-r-4 border-gray-100 bg-white text-center dark:border-gray-600 dark:bg-gray-800">
            <div
                class="items-centre flex justify-end"
                wire:ignore
            >
                {{-- <p class="text-sm font-bold text-gray-700">جمع کل فاکتور</p> --}}
            </div>
            <div class="items-centre flex w-full">
                <input
                    class="block w-full border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    type="text"
                    wire:model="countWeight"
                >
            </div>
            <div
                class="items-centre flex justify-end"
                wire:ignore
            >
                {{-- <p class="text-sm font-bold text-gray-700">تخفیف</p> --}}
            </div>
        </div>
        <div
            class="flex items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-800 md:col-span-3"
            wire:ignore
        >
            {{-- <input type="text" id="title" class="bg-white outline-none border-gray-100 text-center  text-gray-700 text-sm  focus:ring-red-600 focus:border-red-600 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-red-600 dark:focus:border-red-600"> --}}
            <div class="flex w-full flex-col gap-0">
                <div class="items-centre flex justify-end border-b border-gray-200 p-2 dark:border-gray-600">
                    <p class="text-sm font-bold text-gray-700 dark:text-gray-100">جمع کل فاکتور</p>
                </div>
                <div class="items-centre flex justify-end border-b border-gray-200 p-2 dark:border-gray-600">
                    <p class="text-sm font-bold text-gray-700 dark:text-gray-100">بیعانه پرداخت شده</p>
                </div>
                <div class="items-centre flex justify-end p-2">
                    <p class="text-sm font-bold text-gray-700 dark:text-gray-100">تخفیف (رند کردن)</p>
                </div>
            </div>
        </div>
        <div class="flex items-center justify-center bg-gray-100 text-center dark:border-gray-600 md:col-span-2">
            <div class="flex w-full flex-col gap-0">
                <div class="border-b border-gray-300 dark:border-gray-600">
                    <div class="relative">
                        <span class="absolute left-2 top-2">
                            <svg
                                class="inline h-5 w-5 animate-spin text-red-600 dark:text-red-500"
                                role="status"
                                aria-hidden="true"
                                wire:loading
                                wire:target="total"
                                viewBox="0 0 100 101"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="#E5E7EB"
                                />
                                <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </span>
                        <input
                            class="block w-full border-gray-100 bg-gray-200 p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="total"
                            type="text"
                            disabled
                            dir="ltr"
                            onkeyup="javascript:this.value=Comma(this.value);"
                            wire:model="total"
                        >
                    </div>
                </div>
                <div class="border-b border-gray-200 dark:border-gray-600">
                    <div class="relative">
                        <span class="absolute left-2 top-2">
                            <svg
                                class="inline h-5 w-5 animate-spin text-red-600 dark:text-red-500"
                                role="status"
                                aria-hidden="true"
                                wire:loading
                                wire:target="deposit"
                                viewBox="0 0 100 101"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="#E5E7EB"
                                />
                                <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </span>
                        <input
                            class="price block w-full border-2 border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="deposit"
                            type="text"
                            dir="ltr"
                            onkeyup="javascript:this.value=Comma(this.value);"
                            wire:model.live.debounce.700ms="deposit"
                        >
                    </div>
                </div>
                <div class="border-b border-gray-200 dark:border-gray-600">
                    <div class="relative">
                        <span class="absolute left-2 top-2">
                            <svg
                                class="inline h-5 w-5 animate-spin text-red-600 dark:text-red-500"
                                role="status"
                                aria-hidden="true"
                                wire:loading
                                wire:target="discount"
                                viewBox="0 0 100 101"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="#E5E7EB"
                                />
                                <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </span>
                        <input
                            class="price block w-full border-2 border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="discount"
                            type="text"
                            dir="ltr"
                            onkeyup="javascript:this.value=Comma(this.value);"
                            wire:model.live.debounce.700ms="discount"
                        >
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="grid grid-cols-1 gap-0 md:grid-cols-12">
        <div class="flex items-center justify-between bg-gray-100 p-2 dark:border-gray-600 md:col-span-10">
            <p class="text-base font-bold text-gray-700 dark:text-gray-100">مبلغ دریافتی با احتساب ارزش طلا، اجرت، سود،
                مالیات(اجرت+سود) و کسر تخفیفات</p>

            <p class="text-sm font-bold text-gray-700 dark:text-gray-100">مبلغ قابل پرداخت</p>
        </div>
        <div
            class="flex items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600 md:col-span-2">
            <input
                class="block w-full border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="total"
                type="text"
                wire:model.live.debounce.700ms="totalFactor"
            >
        </div>
    </div>
    <div class="grid grid-cols-1">
        <div class="pt-2">
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="description"
            >توضیحات فاکتور (در چاپ فاکتور نمایش داده می شود):</label>
            <div class="relative">
                <svg
                    class="absolute left-2 top-2 inline h-5 w-5 animate-spin text-red-600 dark:text-red-500"
                    role="status"
                    aria-hidden="true"
                    wire:loading
                    wire:target="description"
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                    />
                </svg>
                <textarea
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-red-500 focus:ring-red-500 disabled:bg-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-500 dark:focus:ring-red-500"
                    id="description"
                    rows="3"
                    wire:model.live.debounce.700ms="description"
                ></textarea>
            </div>
        </div>
    </div>
    <div
        class="grid grid-cols-1 gap-0 md:grid-cols-12"
        wire:ignore
    >
        <div class="p-2 dark:border-gray-600 md:col-span-12">
            <p class="text-base font-bold text-red-500">مصنوع فروخته شده بدون دلیل فنی و محکمه پسند به هیچ عنوان تعویض
                و
                یا پس گرفته نمیشود ولی اشتباه از طرفین قابل برگشت و اصلاح میباشد.</p>
        </div>
    </div>
    <div
        class="grid grid-cols-1 gap-0 md:grid-cols-12"
        wire:ignore
    >
        <div
            class="rounded-b-xl border-gray-300 bg-yellow-100 p-2 dark:border-gray-600 dark:bg-gray-900 md:col-span-12">
            <p class="text-base font-bold text-red-500">اعتبار فاکتور تا ساعت 24 تاریخ صدور میباشد</p>
        </div>
    </div>

    <div class="py-3">
        <div class="md:col-span-2 md:p-6">
            <div
                class="pb-6"
                wire:ignore
            >
                <h3 class="text-base font-bold">بارگزاری تصویر شناسایی و محصول</h3>
                <p class="pt-2 text-sm text-gray-400">تصویر شناسنامه و یا کارت ملی و عکس محصول</p>
            </div>
            <div class="grid grid-cols-2 gap-2 md:grid-cols-7">
                {{-- فیلد آپلود تصویر 1 --}}
                <div class="relative">
                    <span
                        class="absolute left-0 top-0 z-10 flex h-full w-full items-center justify-center rounded-xl bg-gray-100 opacity-0 transition-all hover:opacity-100 dark:bg-gray-900"
                        wire:loading
                        wire:target="image1"
                    >
                        @include('layouts.tools.loading-dot')
                    </span>
                    @if ($image1)
                        <div class="relative h-32 w-full overflow-hidden rounded-lg border-2 border-gray-300">
                            <img
                                class="h-full w-full object-cover"
                                src="{{ $image1->temporaryUrl() }}"
                            >
                            <button
                                class="absolute left-1 top-1 z-50 flex h-8 w-8 items-center justify-center rounded-lg bg-white p-1 opacity-90 transition-all hover:bg-red-600 hover:text-white"
                                type="button"
                                wire:click="$set('image1', null)"
                            >
                                <svg
                                    class="h-4 w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    @else
                        <label
                            class="flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
                        >
                            <div class="flex flex-col items-center justify-center pb-6 pt-5">
                                <svg
                                    class="mb-3 h-10 w-10 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                    ></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500">بیعانه 1</p>
                            </div>
                            <input
                                class="hidden"
                                type="file"
                                wire:model="image1"
                                accept="image/*"
                            >
                        </label>
                    @endif
                </div>

                {{-- فیلد آپلود تصویر 2 --}}
                <div class="relative">
                    <span
                        class="absolute left-0 top-0 z-10 flex h-full w-full items-center justify-center rounded-xl bg-gray-100 opacity-0 transition-all hover:opacity-100 dark:bg-gray-900"
                        wire:loading
                        wire:target="image2"
                    >
                        @include('layouts.tools.loading-dot')
                    </span>
                    @if ($image2)
                        <div class="relative h-32 w-full overflow-hidden rounded-lg border-2 border-gray-300">
                            <img
                                class="h-full w-full object-cover"
                                src="{{ $image2->temporaryUrl() }}"
                            >
                            <button
                                class="absolute left-1 top-1 z-50 flex h-8 w-8 items-center justify-center rounded-lg bg-white p-1 opacity-90 transition-all hover:bg-red-600 hover:text-white"
                                type="button"
                                wire:click="$set('image2', null)"
                            >
                                <svg
                                    class="h-4 w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    @else
                        <label
                            class="flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
                        >
                            <div class="flex flex-col items-center justify-center pb-6 pt-5">
                                <svg
                                    class="mb-3 h-10 w-10 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                    ></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500">بیعانه 2</p>
                            </div>
                            <input
                                class="hidden"
                                type="file"
                                wire:model="image2"
                                accept="image/*"
                            >
                        </label>
                    @endif
                </div>

                {{-- فیلد آپلود تصویر 3 --}}
                <div class="relative">
                    <span
                        class="absolute left-0 top-0 z-10 flex h-full w-full items-center justify-center rounded-xl bg-gray-100 opacity-0 transition-all hover:opacity-100 dark:bg-gray-900"
                        wire:loading
                        wire:target="image3"
                    >
                        @include('layouts.tools.loading-dot')
                    </span>
                    @if ($image3)
                        <div class="relative h-32 w-full overflow-hidden rounded-lg border-2 border-gray-300">
                            <img
                                class="h-full w-full object-cover"
                                src="{{ $image3->temporaryUrl() }}"
                            >
                            <button
                                class="absolute left-1 top-1 z-50 flex h-8 w-8 items-center justify-center rounded-lg bg-white p-1 opacity-90 transition-all hover:bg-red-600 hover:text-white"
                                type="button"
                                wire:click="$set('image3', null)"
                            >
                                <svg
                                    class="h-4 w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    @else
                        <label
                            class="flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
                        >
                            <div class="flex flex-col items-center justify-center pb-6 pt-5">
                                <svg
                                    class="mb-3 h-10 w-10 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                    ></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500">نمونه کار</p>
                            </div>
                            <input
                                class="hidden"
                                type="file"
                                wire:model="image3"
                                accept="image/*"
                            >
                        </label>
                    @endif
                </div>

                {{-- فیلد آپلود تصویر 4 --}}
                <div class="relative">
                    <span
                        class="absolute left-0 top-0 z-10 flex h-full w-full items-center justify-center rounded-xl bg-gray-100 opacity-0 transition-all hover:opacity-100 dark:bg-gray-900"
                        wire:loading
                        wire:target="image4"
                    >
                        @include('layouts.tools.loading-dot')
                    </span>
                    @if ($image4)
                        <div class="relative h-32 w-full overflow-hidden rounded-lg border-2 border-gray-300">
                            <img
                                class="h-full w-full object-cover"
                                src="{{ $image4->temporaryUrl() }}"
                            >
                            <button
                                class="absolute left-1 top-1 z-50 flex h-8 w-8 items-center justify-center rounded-lg bg-white p-1 opacity-90 transition-all hover:bg-red-600 hover:text-white"
                                type="button"
                                wire:click="$set('image4', null)"
                            >
                                <svg
                                    class="h-4 w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    @else
                        <label
                            class="flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
                        >
                            <div class="flex flex-col items-center justify-center pb-6 pt-5">
                                <svg
                                    class="mb-3 h-10 w-10 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                    ></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500">ساخت</p>
                            </div>
                            <input
                                class="hidden"
                                type="file"
                                wire:model="image4"
                                accept="image/*"
                            >
                        </label>
                    @endif
                </div>

                {{-- فیلد آپلود تصویر 5 --}}
                <div class="relative">
                    <span
                        class="absolute left-0 top-0 z-10 flex h-full w-full items-center justify-center rounded-xl bg-gray-100 opacity-0 transition-all hover:opacity-100 dark:bg-gray-900"
                        wire:loading
                        wire:target="image5"
                    >
                        @include('layouts.tools.loading-dot')
                    </span>
                    @if ($image5)
                        <div class="relative h-32 w-full overflow-hidden rounded-lg border-2 border-gray-300">
                            <img
                                class="h-full w-full object-cover"
                                src="{{ $image5->temporaryUrl() }}"
                            >
                            <button
                                class="absolute left-1 top-1 z-50 flex h-8 w-8 items-center justify-center rounded-lg bg-white p-1 opacity-90 transition-all hover:bg-red-600 hover:text-white"
                                type="button"
                                wire:click="$set('image5', null)"
                            >
                                <svg
                                    class="h-4 w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    @else
                        <label
                            class="flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
                        >
                            <div class="flex flex-col items-center justify-center pb-6 pt-5">
                                <svg
                                    class="mb-3 h-10 w-10 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                    ></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500">طرح</p>
                            </div>
                            <input
                                class="hidden"
                                type="file"
                                wire:model="image5"
                                accept="image/*"
                            >
                        </label>
                    @endif
                </div>

                {{-- فیلد آپلود تصویر 6 --}}
                <div class="relative">
                    <span
                        class="absolute left-0 top-0 z-10 flex h-full w-full items-center justify-center rounded-xl bg-gray-100 opacity-0 transition-all hover:opacity-100 dark:bg-gray-900"
                        wire:loading
                        wire:target="image6"
                    >
                        @include('layouts.tools.loading-dot')
                    </span>
                    @if ($image6)
                        <div class="relative h-32 w-full overflow-hidden rounded-lg border-2 border-gray-300">
                            <img
                                class="h-full w-full object-cover"
                                src="{{ $image6->temporaryUrl() }}"
                            >
                            <button
                                class="absolute left-1 top-1 z-50 flex h-8 w-8 items-center justify-center rounded-lg bg-white p-1 opacity-90 transition-all hover:bg-red-600 hover:text-white"
                                type="button"
                                wire:click="$set('image6', null)"
                            >
                                <svg
                                    class="h-4 w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    @else
                        <label
                            class="flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
                        >
                            <div class="flex flex-col items-center justify-center pb-6 pt-5">
                                <svg
                                    class="mb-3 h-10 w-10 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                    ></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500">طرح انتخاب مشتری</p>
                            </div>
                            <input
                                class="hidden"
                                type="file"
                                wire:model="image6"
                                accept="image/*"
                            >
                        </label>
                    @endif
                </div>

                {{-- فیلد آپلود تصویر 7 --}}
                <div class="relative">
                    <span
                        class="absolute left-0 top-0 z-10 flex h-full w-full items-center justify-center rounded-xl bg-gray-100 opacity-0 transition-all hover:opacity-100 dark:bg-gray-900"
                        wire:loading
                        wire:target="image7"
                    >
                        @include('layouts.tools.loading-dot')
                    </span>
                    @if ($image7)
                        <div class="relative h-32 w-full overflow-hidden rounded-lg border-2 border-gray-300">
                            <img
                                class="h-full w-full object-cover"
                                src="{{ $image7->temporaryUrl() }}"
                            >
                            <button
                                class="absolute left-1 top-1 z-50 flex h-8 w-8 items-center justify-center rounded-lg bg-white p-1 opacity-90 transition-all hover:bg-red-600 hover:text-white"
                                type="button"
                                wire:click="$set('image7', null)"
                            >
                                <svg
                                    class="h-4 w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    @else
                        <label
                            class="flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
                        >
                            <div class="flex flex-col items-center justify-center pb-6 pt-5">
                                <svg
                                    class="mb-3 h-10 w-10 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                    ></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500">فایل برش</p>
                            </div>
                            <input
                                class="hidden"
                                type="file"
                                wire:model="image7"
                                accept="image/*"
                            >
                        </label>
                    @endif
                </div>

            </div>
        </div>
    </div>
    <form
        class="mt-3 flex items-center justify-between pt-3"
        wire:submit="saveFactor"
    >
        <div
            class="flex w-auto items-center gap-3"
            x-data="{ orders: 'no' }"
        >
            <div class="">
                {{-- <label for="userRefer" class="block mb-2 text-sm outline-none font-bold text-gray-700 dark:text-white">کارشناس مربوطه:</label> --}}
                <select
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-100 p-2 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="userRefer"
                    wire:model="userRefer"
                >
                    <option value="">--انتخاب کارشناس--</option>
                    @foreach ($users as $item)
                        <option value="{{ $item->id }}">{{ $item->fullname . ' (' . $item->mobile . ')' }}
                        </option>
                    @endforeach
                </select>
                @error('userRefer')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>

            <div>
                {{-- <label for="category" class="block mb-2 text-sm font-bold outline-none text-gray-700 dark:text-white">گروه سفارش را انتخاب کنید</label> --}}
                <select
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-100 p-2 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="category"
                    wire:model="category"
                >
                    <option selected>--دسته بندی--</option>
                    <option value="jewelry">گردنبند</option>
                    <option value="bracelet">دستبند</option>
                    <option value="ring">انگشتر</option>
                    <option value="spoon">ست</option>
                    <option value="ankle_jewlery">پابند</option>
                    <option value="jewelery">گوشواره</option>
                </select>
                @error('category')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div>
                {{-- <label for="last_status" class="block mb-2 text-sm font-bold text-white">وضعیت سفارش:</label> --}}
                <select
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-100 p-2 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="last_status"
                    wire:model="last_status"
                >
                    <option selected>--انتخاب کنید--</option>
                    <option value="design">در حال طراحی</option>
                    <option value="wait_design">منتظر انتخاب طرح</option>
                    <option value="cut">فایل برش</option>
                    <option value="ready_to_build">آماده به ساخت</option>
                    <option value="wait_factory">در حال ساخت</option>
                    <option
                        value="ready"
                        style="font-weight:900;color:blue"
                    >آماده به ارسال</option>
                    <option value="ready-on">درحال ارسال</option>
                    <option value="money">منتظر تسویه مشتری</option>
                    <option value="send">ارسال شد</option>
                </select>
                @error('last_status')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>

            <div class="">
                {{-- <label for="post" class="block mb-2 text-sm font-bold outline-none text-gray-700 dark:text-white">ارسال بصورت پست</label> --}}
                <select
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-100 p-2 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="post_type"
                    wire:model.live="post_type"
                >
                    <option
                        value=""
                        selected
                    >--وضعیت ارسال--</option>
                    <option>پست</option>
                    <option>پیک ( تهران و حومه - به عهده مشتری )</option>
                    <option>تحویل حضوری شعبه پونک ( پاساژ همیلا )</option>
                    <option>تحویل حضوری شعبه جنت آباد ( پاساژ الماس )</option>
                </select>
                @error('post_type')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-100 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
        </div>
        <div class="flex items-center gap-2">
            {{-- <a href="{{ route('admin-dashboard-factor-print') }}" target="_blank" class="bg-gray-100 -xl px-4 py-2 text-gray-600 rounded-xl hover:bg-gray-200 transition-all">
                    <span class="text-sm">پیش نمایش فاکتور</span>
                </a> --}}
            <button
                class="rounded-xl bg-red-500 px-4 py-2 text-white transition-all hover:bg-red-600 disabled:bg-gray-200 disabled:text-gray-400"
                type="sumbit"
            >
                <svg
                    class="inline h-6 w-6 animate-spin text-red-700"
                    role="status"
                    aria-hidden="true"
                    wire:loading
                    wire:target="saveFactor"
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                    />
                </svg>
                <span class="text-sm">ذخیره فاکتور و چاپ</span>
            </button>
        </div>
    </form>

</div>
